"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Trash2, Plus } from "lucide-react"
import type { AssessmentData } from "../../types/dashboard"

interface AssessmentTableProps {
  data: AssessmentData[]
}

export function AssessmentTable({ data }: AssessmentTableProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)

  const totalPages = Math.ceil(data.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentData = data.slice(startIndex, endIndex)

  const handleDelete = (id: number) => {
    console.log("Delete item with id:", id)
    // Implement delete functionality
  }

  const handleView = (id: number) => {
    console.log("View item with id:", id)
    // Implement view functionality
  }

  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-lg font-semibold text-[#1e1e1e]">Assessment History</CardTitle>
          <p className="text-xs text-[#64707d] mt-1">
            Review your analytics results and use this information to improve future performance.
          </p>
        </div>
        <Button className="bg-[#6475e9] hover:bg-[#5a6bd8] text-white text-xs">
          <Plus className="w-4 h-4 mr-2" />
          New Assessment
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow className="border-[#eaecf0]">
              <TableHead className="text-[#64707d] font-medium">Nomor</TableHead>
              <TableHead className="text-[#64707d] font-medium">Nama</TableHead>
              <TableHead className="text-[#64707d] font-medium">Tipe Ujian</TableHead>
              <TableHead className="text-[#64707d] font-medium">Tanggal Ujian</TableHead>
              <TableHead className="text-[#64707d] font-medium">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item) => (
              <TableRow key={item.id} className="border-[#eaecf0]">
                <TableCell className="text-[#1e1e1e]">{item.id}</TableCell>
                <TableCell className="text-[#1e1e1e]">{item.nama}</TableCell>
                <TableCell>
                  <Badge variant="secondary" className="bg-[#f3f3f3] text-[#64707d]">
                    {item.tipe}
                  </Badge>
                </TableCell>
                <TableCell className="text-[#64707d]">{item.tanggal}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleView(item.id)}>
                      <ExternalLink className="w-4 h-4 text-[#64707d]" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleDelete(item.id)}>
                      <Trash2 className="w-4 h-4 text-[#64707d]" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-[#64707d]">Show</span>
            <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
              <SelectTrigger className="w-16 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-[#64707d]">Data</span>
          </div>

          <div className="flex gap-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={currentPage === page ? "default" : "ghost"}
                size="sm"
                className={`w-8 h-8 ${
                  currentPage === page ? "bg-[#6475e9] hover:bg-[#5a6bd8] text-white" : "text-[#64707d]"
                }`}
                onClick={() => setCurrentPage(page)}
              >
                {page}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

