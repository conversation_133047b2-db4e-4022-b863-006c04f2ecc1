import { Card, CardContent } from "@/components/ui/card"
import type { StatCard } from "../../types/dashboard"

interface StatsCardProps {
  stat: StatCard
}

export function StatsCard({ stat }: StatsCardProps) {
  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-2xl font-bold text-[#1e1e1e]">{stat.value}</div>
            <div className="text-sm text-[#64707d]">{stat.label}</div>
          </div>
          <div className="w-8 h-8 rounded-full" style={{ backgroundColor: stat.color }}></div>
        </div>
      </CardContent>
    </Card>
  )
}
