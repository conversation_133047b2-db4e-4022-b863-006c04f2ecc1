"use client"

// Update the imports to use the new components and data
import { Head<PERSON> } from "./components/dashboard/header"
import { StatsCard } from "./components/dashboard/stats-card"
import { AssessmentTable } from "./components/dashboard/assessment-table"
import { WorldMapCard } from "./components/dashboard/world-map-card"

import { ProgressCard } from "./components/dashboard/progress-card"
import { assessmentData, statsData, progressData, chartData } from "./data/mockData"

// Replace the existing component content with:
export default function Dashboard() {
  const handleExternalLink = () => {
    window.open("https://example.com", "_blank")
  }

  return (
    <div className="min-h-screen bg-[#f5f7fb] p-6 flex items-center justify-center">
      <div className="max-w-7xl mx-auto space-y-6 w-full">
        {/* Header */}
        <Header
          title="Lorem ipsum dolor sit amet"
          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. Aliquam in hendrerit urna."
          onExternalLinkClick={handleExternalLink}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {statsData.map((stat) => (
                <StatsCard key={stat.id} stat={stat} />
              ))}
            </div>

            {/* Assessment History */}
            <AssessmentTable data={assessmentData} />
          </div>

          {/* Right Sidebar */}
          <div className="space-y-6">
            <WorldMapCard
              title="{User Name}"
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
            />


            <ProgressCard
              title="Lorem ipsum dolor sit amet"
              description="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
              data={progressData}
            />
            

          </div>
        </div>
      </div>
    </div>
  )
}

