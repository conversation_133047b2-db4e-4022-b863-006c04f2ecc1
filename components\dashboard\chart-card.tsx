import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import type { ChartData } from "../../types/dashboard"

interface ChartCardProps {
  title: string
  description: string
  data: ChartData[]
}

export function ChartCard({ title, description, data }: ChartCardProps) {
  const maxValue = Math.max(...data.map(item => item.value))
  
  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-[#1e1e1e]">{title}</CardTitle>
        <p className="text-sm text-[#64707d]">{description}</p>
      </CardHeader>
      <CardContent>
        <div className="flex items-end justify-center gap-2 h-32 mb-6">
          {data.map((item, index) => {
            const height = (item.value / maxValue) * 100
            return (
              <div key={index} className="flex flex-col items-center gap-2">
                <div 
                  className="w-8 rounded-t-lg transition-all duration-300"
                  style={{ 
                    height: `${height}%`,
                    backgroundColor: item.color,
                    minHeight: '30px'
                  }}
                />
                <div className="w-2 h-2 rounded-full bg-[#e5e7eb]" />
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}

