import { Card, CardContent } from "@/components/ui/card"
import { Globe } from "lucide-react"

interface WorldMapCardProps {
  title: string
  description: string
}

export function WorldMapCard({ title, description }: WorldMapCardProps) {
  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardContent className="flex flex-col space-y-1.5 p-6">
        <div className="flex items-center justify-center mb-4">
          <div className="w-24 h-24 rounded-full border-4 border-[#6475e9] flex items-center justify-center bg-gradient-to-br from-[#6475e9] to-[#a2acf2]">
            <Globe className="w-12 h-12 text-white opacity-80" />
          </div>
        </div>

        <div className="text-center">
          <h3 className="text-lg font-semibold text-[#1e1e1e] mb-2">{title}</h3>
          <p className="text-sm text-[#64707d] mb-4">{description}</p>
        </div>

        <div className="flex items-end justify-center gap-2 h-32 mb-6">
          <div className="flex flex-col items-center gap-2">
            <div
              className="w-16 rounded-t-lg transition-all duration-300"
              style={{
                height: '25%',
                backgroundColor: '#d0d5dd',
                minHeight: '40px'
              }}
            />
            <div className="w-2 h-2 rounded-full bg-[#e5e7eb]" />
          </div>
          <div className="flex flex-col items-center gap-2">
            <div
              className="w-16 rounded-t-lg transition-all duration-300"
              style={{
                height: '37.5%',
                backgroundColor: '#6475e9',
                minHeight: '65px'
              }}
            />
            <div className="w-2 h-2 rounded-full bg-[#e5e7eb]" />
          </div>
          <div className="flex flex-col items-center gap-2">
            <div
              className="w-16 rounded-t-lg transition-all duration-300"
              style={{
                height: '50%',
                backgroundColor: '#9e9e9e',
                minHeight: '34px'
              }}
            />
            <div className="w-2 h-2 rounded-full bg-[#e5e7eb]" />
          </div>
          <div className="flex flex-col items-center gap-2">
            <div
              className="w-16 rounded-t-lg transition-all duration-300"
              style={{
                height: '31.25%',
                backgroundColor: '#6475e9',
                minHeight: '87px'
              }}
            />
            <div className="w-2 h-2 rounded-full bg-[#e5e7eb]" />
          </div>
          <div className="flex flex-col items-center gap-2">
            <div
              className="w-16 rounded-t-lg transition-all duration-300"
              style={{
                height: '18.75%',
                backgroundColor: '#a2acf2',
                minHeight: '67px'
              }}
            />
            <div className="w-2 h-2 rounded-full bg-[#e5e7eb]" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
