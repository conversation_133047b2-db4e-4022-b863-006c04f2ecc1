"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ExternalLink, BarChart3 } from "lucide-react"

interface HeaderProps {
  title: string
  description: string
  onExternalLinkClick?: () => void
}

export function Header({ title, description, onExternalLinkClick }: HeaderProps) {
  return (
    <div className="flex items-start justify-between">
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-[#6475e9] rounded-full flex items-center justify-center">
          <BarChart3 className="w-6 h-6 text-white" />
        </div>
        <div>
          <h1 className="text-2xl font-semibold text-[#1e1e1e]">{title}</h1>
          <p className="text-[#64707d] text-sm mt-1">{description}</p>
        </div>
      </div>
      <Button variant="outline" size="icon" className="border-[#eaecf0] bg-transparent" onClick={onExternalLinkClick}>
        <ExternalLink className="w-4 h-4" />
      </Button>
    </div>
  )
}
